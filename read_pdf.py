import pdfplumber
import os

def read_pdf_content(pdf_path):
    if not os.path.exists(pdf_path):
        return f"文件不存在: {pdf_path}"
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            print(f"PDF总页数: {len(pdf.pages)}")
            text = ""
            for i, page in enumerate(pdf.pages[:5]):  # 只读前5页
                page_text = page.extract_text()
                if page_text:
                    text += f"\n=== 第{i+1}页 ===\n"
                    text += page_text
                    print(f"第{i+1}页内容长度: {len(page_text)}")
            return text
    except Exception as e:
        return f"读取PDF失败: {e}"

# 读取第二个PDF文件
pdf_path = r"d:\code\wx\k12-dev-tool\Ai\upload_temp\幼小衔接拼音+300字全攻略.pdf"
content = read_pdf_content(pdf_path)
print("\n文件内容:")
print(content[:2000])  # 显示前2000字符
print("...")
