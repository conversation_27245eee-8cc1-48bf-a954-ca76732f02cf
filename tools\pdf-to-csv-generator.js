/**
 * PDF到CSV生成器 - 基于AI.md规范
 * 使用Augment AI IDE自动化处理PDF文件并生成标准CSV
 */

const fs = require('fs');
const path = require('path');
const pdfParse = require('pdf-parse');
const csvWriter = require('csv-writer');

class PDFToCSVGenerator {
  constructor() {
    // AI.md规范配置
    this.csvHeaders = [
      '文件路径', '标题', '描述', '分类', '年级', '科目', '册别',
      '板块', '标签', '文件特征', '状态', '排序权重', '广告次数',
      '下载次数', '查看次数'
    ];

    // 预定义标签库
    this.tagLibrary = [
      '基础练习', '提高练习', '重点推荐', '热门下载', '精品资源',
      '期末复习', '日常练习', '完整版', '习惯养成', '开学准备'
    ];

    // 文件特征库
    this.featureLibrary = [
      '高清版', '可打印', '含答案', '含解析', '彩色版', '黑白版',
      '教师版', '学生版', '完整版', '精简版'
    ];

    // 年级识别模式
    this.gradePatterns = {
      '幼升小': /幼升小|幼小衔接|入学准备/,
      '一年级': /一年级|1年级/,
      '二年级': /二年级|2年级/,
      '三年级': /三年级|3年级/,
      '四年级': /四年级|4年级/,
      '五年级': /五年级|5年级/,
      '六年级': /六年级|6年级/,
      '小升初': /小升初|升学/
    };

    // 科目识别模式
    this.subjectPatterns = {
      '语文': /语文|拼音|识字|汉字|作文|阅读/,
      '数学': /数学|算术|计算|几何/,
      '英语': /英语|English/,
      '科学': /科学|自然/,
      '音乐': /音乐|歌曲/,
      '美术': /美术|绘画|手工/,
      '体育': /体育|运动/
    };
  }

  /**
   * 批量处理PDF文件夹
   */
  async processPDFFolder(folderPath) {
    const results = [];
    const files = fs.readdirSync(folderPath);

    for (const file of files) {
      if (path.extname(file).toLowerCase() === '.pdf') {
        const filePath = path.join(folderPath, file);
        console.log(`处理文件: ${file}`);

        try {
          const result = await this.processSinglePDF(filePath);
          results.push(result);
        } catch (error) {
          console.error(`处理文件 ${file} 失败:`, error.message);
        }
      }
    }

    return results;
  }

  /**
   * 处理单个PDF文件
   */
  async processSinglePDF(filePath) {
    const fileName = path.basename(filePath);
    const buffer = fs.readFileSync(filePath);

    // 解析PDF内容
    const pdfData = await pdfParse(buffer);
    const content = pdfData.text;

    // AI智能分析文件属性
    const analysis = this.analyzeFileContent(fileName, content);

    return {
      文件路径: `[FOLDER_PATH]\\${fileName}`,
      标题: this.generateTitle(analysis),
      描述: this.generateDescription(analysis),
      分类: analysis.category,
      年级: analysis.grade,
      科目: analysis.subject,
      册别: analysis.volume,
      板块: analysis.section,
      标签: analysis.tags.join(';'),
      文件特征: analysis.features.join(';'),
      状态: 'active',
      排序权重: '0',
      广告次数: '1',
      下载次数: Math.floor(Math.random() * 101).toString(),
      查看次数: Math.floor(Math.random() * 701 + 300).toString()
    };
  }

  /**
   * AI智能分析文件内容
   */
  analyzeFileContent(fileName, content) {
    const analysis = {
      fileName: fileName,
      content: content,
      grade: this.detectGrade(fileName, content),
      subject: this.detectSubject(fileName, content),
      volume: this.detectVolume(fileName, content),
      hasAnswers: this.detectAnswers(fileName, content),
      hasAnalysis: this.detectAnalysis(fileName, content),
      contentType: this.detectContentType(fileName, content),
      difficulty: this.detectDifficulty(fileName, content),
      keywords: this.extractKeywords(fileName, content)
    };

    // 确定分类
    analysis.category = (analysis.grade === '幼升小' || analysis.grade === '小升初')
      ? 'upgrade' : 'regular';

    // 确定板块
    analysis.section = this.determineSection(analysis);

    // 生成标签
    analysis.tags = this.generateTags(analysis);

    // 生成文件特征
    analysis.features = this.generateFeatures(analysis);

    return analysis;
  }

  /**
   * 检测年级
   */
  detectGrade(fileName, content) {
    for (const [grade, pattern] of Object.entries(this.gradePatterns)) {
      if (pattern.test(fileName) || pattern.test(content.substring(0, 500))) {
        return grade;
      }
    }
    return '一年级'; // 默认值
  }

  /**
   * 检测科目
   */
  detectSubject(fileName, content) {
    for (const [subject, pattern] of Object.entries(this.subjectPatterns)) {
      if (pattern.test(fileName) || pattern.test(content.substring(0, 500))) {
        return subject;
      }
    }
    return '语文'; // 默认值
  }

  /**
   * 检测册别
   */
  detectVolume(fileName, content) {
    if (/上册|上学期/.test(fileName)) return '上册';
    if (/下册|下学期/.test(fileName)) return '下册';
    return '全册';
  }

  /**
   * 检测是否含答案
   */
  detectAnswers(fileName, content) {
    const answerPatterns = /答案|附答案|参考答案|标准答案|详细答案/;
    return answerPatterns.test(fileName) || answerPatterns.test(content);
  }

  /**
   * 检测是否含解析
   */
  detectAnalysis(fileName, content) {
    const analysisPatterns = /解析|详解|解答过程|解题步骤|思路分析/;
    return analysisPatterns.test(fileName) || analysisPatterns.test(content);
  }

  /**
   * 检测内容类型
   */
  detectContentType(fileName, content) {
    const types = [];
    if (/练习|训练|作业/.test(fileName) || /练习|训练|作业/.test(content.substring(0, 1000))) {
      types.push('练习');
    }
    if (/试卷|测试|考试/.test(fileName) || /试卷|测试|考试/.test(content.substring(0, 1000))) {
      types.push('试卷');
    }
    if (/知识点|总结|梳理/.test(fileName) || /知识点|总结|梳理/.test(content.substring(0, 1000))) {
      types.push('知识点');
    }
    if (/教案|教学/.test(fileName) || /教案|教学/.test(content.substring(0, 1000))) {
      types.push('教案');
    }
    return types.length > 0 ? types : ['资料'];
  }

  /**
   * 检测难度等级
   */
  detectDifficulty(fileName, content) {
    if (/基础|入门|启蒙/.test(fileName) || /基础|入门|启蒙/.test(content.substring(0, 1000))) {
      return '基础';
    }
    if (/提高|进阶|拔尖/.test(fileName) || /提高|进阶|拔尖/.test(content.substring(0, 1000))) {
      return '提高';
    }
    if (/冲刺|满分|重点/.test(fileName) || /冲刺|满分|重点/.test(content.substring(0, 1000))) {
      return '冲刺';
    }
    return '标准';
  }

  /**
   * 提取关键词
   */
  extractKeywords(fileName, content) {
    const keywords = new Set();
    const text = fileName + ' ' + content.substring(0, 2000);

    // 学习内容关键词
    const contentKeywords = ['拼音', '识字', '汉字', '生字', '词语', '句子', '作文', '阅读',
                           '数字', '计算', '加法', '减法', '乘法', '除法', '几何', '应用题'];

    contentKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        keywords.add(keyword);
      }
    });

    // 教学特色关键词
    const featureKeywords = ['同步', '专项', '系统', '全面', '重点', '难点', '易错', '口诀'];

    featureKeywords.forEach(keyword => {
      if (text.includes(keyword)) {
        keywords.add(keyword);
      }
    });

    return Array.from(keywords);
  }

  /**
   * 确定板块
   */
  determineSection(analysis) {
    if (analysis.category === 'upgrade') {
      if (analysis.subject === '语文') {
        if (/拼音/.test(analysis.fileName) || analysis.keywords.includes('拼音')) return '拼音启蒙';
        if (/识字|汉字/.test(analysis.fileName) || analysis.keywords.includes('识字')) return '学科启蒙';
        return '学科启蒙';
      }
      if (analysis.subject === '数学') {
        if (/数字|计算/.test(analysis.fileName) || analysis.keywords.includes('数字')) return '认识数字';
        return '知识科普';
      }
      return '知识科普';
    } else {
      if (analysis.contentType.includes('试卷')) return '试卷';
      if (analysis.contentType.includes('练习') && /专项/.test(analysis.fileName)) return '专项练习';
      if (/单元/.test(analysis.fileName) || analysis.keywords.includes('同步')) return '单元同步';
      if (analysis.contentType.includes('知识点')) return '核心知识点';
      if (/单元/.test(analysis.fileName)) return '单元知识点';
      return '专项练习';
    }
  }

  /**
   * 生成标题
   */
  generateTitle(analysis) {
    const prefixes = ['【精品】', '【必备】', '【热门】', '【冲刺】', '【提分】'];
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];

    return `${prefix}${analysis.grade}${analysis.subject}${analysis.volume}必备资料`;
  }

  /**
   * 生成描述
   */
  generateDescription(analysis) {
    const templates = [
      `同步教材重点知识，助力孩子轻松掌握${analysis.subject}基础`,
      `系统梳理${analysis.grade}${analysis.subject}重点难点，提升学习效率`,
      `精选优质练习题，让孩子${analysis.subject}成绩快速提升`
    ];

    return templates[Math.floor(Math.random() * templates.length)];
  }

  /**
   * 生成标签
   */
  generateTags(analysis) {
    const tags = ['基础练习'];

    if (Math.random() > 0.5) tags.push('重点推荐');
    if (Math.random() > 0.6) tags.push('热门下载');
    if (analysis.volume !== '全册') tags.push('期末复习');

    return tags.slice(0, 3);
  }

  /**
   * 生成文件特征
   */
  generateFeatures(analysis) {
    const features = ['高清版', '可打印'];

    if (analysis.hasAnswers) features.push('含答案');
    if (analysis.hasAnalysis) features.push('含解析');

    return features;
  }

  /**
   * 导出CSV文件
   */
  async exportToCSV(data, outputPath) {
    const writer = csvWriter.createObjectCsvWriter({
      path: outputPath,
      header: this.csvHeaders.map(header => ({
        id: header,
        title: header
      })),
      encoding: 'utf8'
    });

    await writer.writeRecords(data);
    console.log(`CSV文件已生成: ${outputPath}`);
  }
}

module.exports = PDFToCSVGenerator;

// 使用示例
if (require.main === module) {
  const generator = new PDFToCSVGenerator();

  async function main() {
    try {
      const pdfFolder = './pdf-files'; // PDF文件夹路径
      const outputCSV = './output/generated.csv'; // 输出CSV路径

      console.log('开始处理PDF文件...');
      const results = await generator.processPDFFolder(pdfFolder);

      console.log(`处理完成，共生成 ${results.length} 条记录`);
      await generator.exportToCSV(results, outputCSV);

    } catch (error) {
      console.error('处理失败:', error);
    }
  }

  main();
}
